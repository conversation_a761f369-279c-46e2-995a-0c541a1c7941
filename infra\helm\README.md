# Application Helm Chart

This Helm chart deploys the application in a Kubernetes cluster. It is configured for local development with Rancher Desktop by default, but includes environment-specific configurations for various deployment targets.

## Prerequisites

- Kubernetes cluster (Rancher Desktop for local development)
- Helm 3.0+
- kubectl configured to communicate with your cluster

## Installation

### Local Development

```bash
# Install the chart with default values and local specific values (local development)
helm secrets install myapp . -f values/values.yaml -f values/values.dev.yaml -f secrets/dev.yaml
```


### Upgrade Existing Deployment

```bash
# Upgrade an existing deployment
helm secrets upgrade myapp . -f values/values.yaml -f values/values.dev.yaml -f secrets/dev.yaml
```

### Uninstall

```bash
# Remove the deployment
helm uninstall myapp
```

## Local Development Setup

For local development with Rancher Desktop:

1. Install the chart:
   ```bash
   helm install myapp .
   ```

2. Access your application at http://localhost

## Customization

### Custom Values

You can override any of the values during installation:

```bash
helm install myapp . --set replicaCount=3 --set image.tag=mytag
```
