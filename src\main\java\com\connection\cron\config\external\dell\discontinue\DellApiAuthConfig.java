package com.connection.cron.config.external.dell.discontinue;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "dell.api.auth")
public class DellApiAuthConfig {
  @Value("${dell.api.auth.url}")
  private String authUrl;

  @Value("${dell.api.auth.client}")
  private String client;

  @Value("${dell.api.auth.secret}")
  private String secret;

  @Value("${dell.api.auth.refresh}")
  private int refreshInterval;

  @Value("${dell.api.auth.grantType}")
  private String grantType;
}
