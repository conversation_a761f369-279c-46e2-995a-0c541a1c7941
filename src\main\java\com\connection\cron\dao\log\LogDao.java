package com.connection.cron.dao.log;

import com.connection.cron.dao.base.AbstractDataAccessObject;
import com.connection.cron.po.log.LogPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.namedparam.SqlParameterSourceUtils;
import org.springframework.stereotype.Component;
import java.util.List;

@Component
@Slf4j
public class LogDao extends AbstractDataAccessObject {

  private static final String INSERT_LOG = """
      INSERT INTO dbase.log(
              action, field_names, field_values_new, field_values_old, index_fields, invoice_num, log_date, log_time,
              order_num, order_type, program, table_name, user_id,
              field_names__1, field_names__2, field_names__3, field_names__4, field_names__5, field_names__6,
              field_names__7, field_names__8, field_names__9, field_names__10,
              field_values_old__1, field_values_old__2, field_values_old__3, field_values_old__4, field_values_old__5,
              field_values_old__6, field_values_old__7, field_values_old__8, field_values_old__9, field_values_old__10,
              field_values_new__1, field_values_new__2, field_values_new__3, field_values_new__4, field_values_new__5,
              field_values_new__6, field_values_new__7, field_values_new__8, field_values_new__9, field_values_new__10)
              VALUES (:action, :fieldNames, :fieldValuesNew, :fieldValuesOld, :indexFields, :invoiceNum, :logDate, :logTime,
              :orderNum, :orderType, :program, :tableName, :userId,
              :fieldNames1, :fieldNames2, :fieldNames3, :fieldNames4, :fieldNames5, :fieldNames6,
              :fieldNames7, :fieldNames8, :fieldNames9, :fieldNames10,
              :fieldValuesOld1, :fieldValuesOld2, :fieldValuesOld3, :fieldValuesOld4, :fieldValuesOld5,
              :fieldValuesOld6, :fieldValuesOld7, :fieldValuesOld8, :fieldValuesOld9, :fieldValuesOld10,
              :fieldValuesNew1, :fieldValuesNew2, :fieldValuesNew3, :fieldValuesNew4, :fieldValuesNew5,
              :fieldValuesNew6, :fieldValuesNew7, :fieldValuesNew8, :fieldValuesNew9, :fieldValuesNew10)
      """;

  public void batchInsertLogs(List<LogPo> logs) {
    getNamedParameterJdbcTemplate().batchUpdate(INSERT_LOG, SqlParameterSourceUtils.createBatch(logs));
  }
}
