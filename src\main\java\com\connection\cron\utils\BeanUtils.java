package com.connection.cron.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class BeanUtils {
  public static void copyPropertiesIgnoreErrors(Object target, Object source) {
    try {
      org.apache.commons.beanutils.BeanUtils.copyProperties(target, source);
    } catch (Exception e) {
      log.warn("had issues copying beans {}", e.getMessage());
    }
  }
}