package com.connection.cron.po.log;

import lombok.*;
import java.sql.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogPo {
  private String action;
  private String fieldNames;
  private String fieldValuesNew;
  private String fieldValuesOld;
  private String indexFields;
  private Integer invoiceNum;
  private Date logDate;
  private Integer logTime;
  private Integer orderNum;
  private String orderType;
  private String program;
  private String tableName;
  private String userId;
  private String fieldNames1;
  private String fieldNames2;
  private String fieldNames3;
  private String fieldNames4;
  private String fieldNames5;
  private String fieldNames6;
  private String fieldNames7;
  private String fieldNames8;
  private String fieldNames9;
  private String fieldNames10;
  private String fieldValuesOld1;
  private String fieldValuesOld2;
  private String fieldValuesOld3;
  private String fieldValuesOld4;
  private String fieldValuesOld5;
  private String fieldValuesOld6;
  private String fieldValuesOld7;
  private String fieldValuesOld8;
  private String fieldValuesOld9;
  private String fieldValuesOld10;
  private String fieldValuesNew1;
  private String fieldValuesNew2;
  private String fieldValuesNew3;
  private String fieldValuesNew4;
  private String fieldValuesNew5;
  private String fieldValuesNew6;
  private String fieldValuesNew7;
  private String fieldValuesNew8;
  private String fieldValuesNew9;
  private String fieldValuesNew10;
}