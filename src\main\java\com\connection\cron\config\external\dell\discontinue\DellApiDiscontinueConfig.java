package com.connection.cron.config.external.dell.discontinue;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "dell.api.discontinue")
public class DellApiDiscontinueConfig {

  @Value("${dell.api.discontinue.url}")
  private String discontinueUrl;

  @Value("${dell.api.discontinue.lang}")
  private String discontinueLang;

}