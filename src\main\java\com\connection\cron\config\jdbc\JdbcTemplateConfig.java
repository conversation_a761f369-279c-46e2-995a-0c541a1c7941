package com.connection.cron.config.jdbc;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import javax.sql.DataSource;

@Configuration
public class JdbcTemplateConfig {

  @Bean(name = "mysqlJdbcTemplate")
  public JdbcTemplate mysqlJdbcTemplate(@Qualifier("mysqlDataSource") DataSource mysqlDataSource) {
    return new JdbcTemplate(mysqlDataSource);
  }

  @Bean(name = "progressJdbcTemplate")
  public JdbcTemplate progressJdbcTemplate(@Qualifier("progressDataSource") DataSource progressDataSource) {
    return new JdbcTemplate(progressDataSource);
  }
}