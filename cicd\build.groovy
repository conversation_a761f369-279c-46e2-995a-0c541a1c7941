pipeline {
    agent {
        label 'TPMH'
    }

    parameters {
        string(name: 'BRANCH', defaultValue: 'develop')
        string(name: 'RELEASE')
        string(
            name: 'VERSION',
            description: 'Sets the tag of the container image. Multiple versions are allowed. Use spaces to separate versions.'
        )
    }

    environment {
        REGISTRY = 'connection-cnxn-docker-local.jfrog.io'
        IMAGE_NAME = 'cron-runner'
    }

    stages {
        stage('Checkout api project') {
            steps {
                checkout([$class: 'GitSCM', branches: [[name: "*/$BRANCH"]],
                    doGenerateSubmoduleConfigurations: false,
                    submoduleCfg: [],
                    userRemoteConfigs: [[
                        credentialsId: '8edb6623-47ff-4239-8ee3-8a8f3ceabb55',
                        url: 'https://versioncontrol:8443/com/moredirect/service'
                    ]]
                ])
            }
        }

        stage('Prepare Tags') {
            steps {
                script {
                    versions = params.VERSION.tokenize(' ')
                    if (versions.isEmpty()) {
                        error "VERSION parameter must not be empty"
                    }
                    env.IMAGE_TAG = versions[0]
                    env.ALL_TAGS = versions.join(' ')
                }
            }
        }

        stage('Login to Registry') {
            steps {
                withCredentials([usernamePassword(
                    credentialsId: 'artifactory-pfe-remote',
                    usernameVariable: 'REGISTRY_USER',
                    passwordVariable: 'REGISTRY_PASS'
                )]) {
                    sh "docker login -u $REGISTRY_USER -p $REGISTRY_PASS $REGISTRY"
                }
            }
        }

        stage('Build Image') {
            steps {
                withCredentials([usernamePassword(
                    credentialsId: 'ces-api-write',
                    usernameVariable: 'MVN_USER',
                    passwordVariable: 'MVN_PASSWORD'
                )]) {
                    sh '''
                        # Build the JAR from source
                        ./gradlew clean build -x test

                        # Copy the built JAR to the Docker context
                        cd cicd/images
                        cp ../../build/libs/*.jar app.jar

                        # Build the Docker image
                        docker build -t $REGISTRY/$IMAGE_NAME:$IMAGE_TAG .
                    '''
                }
            }
        }

        stage('Tag Extra Versions') {
            steps {
                script {
                    def tags = env.ALL_TAGS.tokenize(' ')
                    def base = "${env.REGISTRY}/${env.IMAGE_NAME}:${tags[0]}"
                    tags[1..-1].each { tag ->
                        def fullTag = "${env.REGISTRY}/${env.IMAGE_NAME}:${tag}"
                        sh "docker tag ${base} ${fullTag}"
                    }
                }
            }
        }

        stage('Push Image') {
            steps {
                script {
                    def tags = env.ALL_TAGS.tokenize(' ')
                    tags.each { tag ->
                        def fullTag = "${env.REGISTRY}/${env.IMAGE_NAME}:${tag}"
                        sh "docker push ${fullTag}"
                    }
                }
            }
        }
    }

    post {
        always {
            sh "docker logout $REGISTRY"
            cleanWs()
        }
    }
}
