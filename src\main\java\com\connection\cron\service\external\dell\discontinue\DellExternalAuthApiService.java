package com.connection.cron.service.external.dell.discontinue;

import com.connection.cron.config.external.dell.discontinue.DellApiAuthConfig;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientRequestException;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

@Service
@Slf4j
public class DellExternalAuthApiService {

  @Autowired
  private DellApiAuthConfig config;

  private final AtomicReference<String> jwtToken = new AtomicReference<>();

  @Autowired
  private WebClient.Builder webClientBuilder;

  private WebClient webClient;

  @PostConstruct
  public void init() {
    this.webClient = webClientBuilder.baseUrl(config.getAuthUrl())
        .build();
    refreshJwtToken();
  }

  private void refreshJwtToken() {
    String token = webClient.post()
        .uri(buildUrlParams(config.getAuthUrl()))
        .header(HttpHeaders.USER_AGENT, "Connection's API")
        .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE)
        .retrieve()
        .onStatus(status -> status.is4xxClientError() || status.is5xxServerError(),
            clientResponse -> clientResponse.bodyToMono(String.class)
                .flatMap(errorBody -> {
                  log.warn("Request failed with status {} and message {}", clientResponse.statusCode(), errorBody);
                  return Mono.error(new RuntimeException("Request failed: " + errorBody));
                }))
        .bodyToMono(String.class)
        .map(body -> new JSONObject(body).getString("access_token"))
        .doOnError(WebClientRequestException.class, e -> {
          // Handle connection errors (like timeouts)
          log.error("Request error during token refresh {}", e.getMessage());
        })
        .doOnError(WebClientResponseException.class, e -> {
          // Handle unexpected HTTP responses
          log.error("Response error during token refresh: {} ", e.getStatusCode() + " - " + e.getMessage());
        })
        .onErrorResume(e -> {
          // Log and handle any unexpected errors with a fallback
          log.error("Unexpected error during token refresh: {}", e.getMessage());
          return Mono.empty(); // Fallback to an empty response
        })
        .block(Duration.ofSeconds(5));

    if (token != null) {
      jwtToken.set(token);
    } else {
      log.error("Token refresh failed: No token was retrieved");
    }
  }

  // Scheduled to refresh the token at the specified interval
  @Scheduled(fixedRateString = "${dell.api.auth.refresh}")
  public void scheduledTokenRefresh() {
    refreshJwtToken();
  }

  public String getJwtToken() {
    return jwtToken.get();
  }

  private String buildUrlParams(String url) {
    Map<Object, Object> data = new HashMap<>();
    data.put("grant_type", config.getGrantType());
    data.put("client_id", config.getClient());
    data.put("client_secret", config.getSecret());
    var builder = new StringBuilder(url);
    builder.append("?");
    for (Map.Entry<Object, Object> entry : data.entrySet()) {
      if (!builder.isEmpty()) {
        builder.append("&");
      }
      builder.append(URLEncoder.encode(entry.getKey()
          .toString(), StandardCharsets.UTF_8));
      builder.append("=");
      builder.append(URLEncoder.encode(entry.getValue()
          .toString(), StandardCharsets.UTF_8));
    }
    return builder.toString();
  }
}
