package com.connection.cron;

import com.connection.cron.annotation.timing.LogMethodExecutionTime;
import com.connection.cron.job.CronJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class CronRunnerManager {

  @Autowired
  @Qualifier("DiscontinueExpiredDellQuotePartsCronJob")
  private CronJob discontinueExpiredDellQuotePartsCronJob;

  @Scheduled(cron = "${scheduled.cron-job.DiscontinueExpiredDellQuotePartsCronJob}", zone = "America/New_York")
  @LogMethodExecutionTime
  public void runDiscontinueExpiredDellQuotePartsCronJob() {
    discontinueExpiredDellQuotePartsCronJob.execute();
  }
}
