package com.connection.cron.config.jdbc.mysql;

import com.zaxxer.hikari.HikariDataSource;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import javax.sql.DataSource;

@Configuration
public class MySQLDataSourceConfig {

  @Bean(name = "mysqlDataSource")
  @Primary
  @ConfigurationProperties("spring.datasource.mysql")
  public DataSource mysqlDataSource() {
    return new HikariDataSource();
  }
}