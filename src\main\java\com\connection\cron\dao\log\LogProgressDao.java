package com.connection.cron.dao.log;

import com.connection.cron.dao.base.AbstractProgressDataAccessObject;
import com.connection.cron.po.log.LogPo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import java.sql.PreparedStatement;
import java.util.List;

@Component
public class LogProgressDao extends AbstractProgressDataAccessObject {

  private static final String INSERT_LOG = """
        INSERT INTO PUB."log"
        ("user-id", "log-date", "log-time", "program", "index-fields", "field-names", "field-values-old", "field-values-new", "order-type", "order-num", "invoice-num", "table-name", "action")
        VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      """;
  private static final String FIELD_NAME_SEPARATOR = ";";

  public void batchInsertLogs(List<LogPo> logPos) {
     getMysqlJdbcTemplate().batchUpdate(INSERT_LOG, logPos, logPos.size(), (PreparedStatement ps, LogPo log) -> {
      ps.setString(1, log.getUserId());
      ps.setDate(2, log.getLogDate());
      ps.setInt(3, log.getLogTime());
      ps.setString(4, log.getProgram());
      ps.setString(5, log.getIndexFields());
      ps.setString(6, StringUtils.join(
          new String[]{log.getFieldNames1(), log.getFieldNames2(), log.getFieldNames3(), log.getFieldNames4(),
              log.getFieldNames5(), log.getFieldNames6(), log.getFieldNames7(), log.getFieldNames8(),
              log.getFieldNames9(), log.getFieldNames10()}, FIELD_NAME_SEPARATOR));
      ps.setString(7, StringUtils.join(new String[]{replaceFieldValueSeparator(log.getFieldValuesOld1()),
          replaceFieldValueSeparator(log.getFieldValuesOld2()), replaceFieldValueSeparator(log.getFieldValuesOld3()),
          replaceFieldValueSeparator(log.getFieldValuesOld4()), replaceFieldValueSeparator(log.getFieldValuesOld5()),
          replaceFieldValueSeparator(log.getFieldValuesOld6()), replaceFieldValueSeparator(log.getFieldValuesOld7()),
          replaceFieldValueSeparator(log.getFieldValuesOld8()), replaceFieldValueSeparator(log.getFieldValuesOld9()),
          replaceFieldValueSeparator(log.getFieldValuesOld10())}, FIELD_NAME_SEPARATOR));
      ps.setString(8, StringUtils.join(new String[]{replaceFieldValueSeparator(log.getFieldValuesNew1()),
          replaceFieldValueSeparator(log.getFieldValuesNew2()), replaceFieldValueSeparator(log.getFieldValuesNew3()),
          replaceFieldValueSeparator(log.getFieldValuesNew4()), replaceFieldValueSeparator(log.getFieldValuesNew5()),
          replaceFieldValueSeparator(log.getFieldValuesNew6()), replaceFieldValueSeparator(log.getFieldValuesNew7()),
          replaceFieldValueSeparator(log.getFieldValuesNew8()), replaceFieldValueSeparator(log.getFieldValuesNew9()),
          replaceFieldValueSeparator(log.getFieldValuesNew10())}, FIELD_NAME_SEPARATOR));
      ps.setString(9, log.getOrderType());
      ps.setInt(10, log.getOrderNum());
      ps.setInt(11, log.getInvoiceNum());
      ps.setString(12, log.getTableName());
      ps.setString(13, log.getAction());
    });
  }

  private static String replaceFieldValueSeparator(String value) {
    return StringUtils.replace(value, FIELD_NAME_SEPARATOR, " ");
  }

}
