package com.connection.cron.service.mditem;

import static org.apache.commons.collections.CollectionUtils.isNotEmpty;

import com.connection.cron.dao.mditem.MdItemDao;
import com.connection.cron.dao.mditem.MdItemProgressDao;
import com.connection.cron.po.mditem.MdItemPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Service
public class MdItemService {

  @Autowired
  private MdItemDao mdItemDao;

  @Autowired
  private MdItemProgressDao mdItemProgressDao;

  @Transactional
  public void batchDiscontinue(List<MdItemPo> mdItems) {
    if (isNotEmpty(mdItems)) {
      mdItemProgressDao.batchDiscontinue(mdItems);
      mdItemDao.batchDiscontinue(mdItems);
    }
  }
}
