package com.connection.cron.service.supplierPart;

import com.connection.cron.dao.supplierParts.SupplierPartsDao;
import com.connection.cron.dto.supplierPart.SupplierPartDto;
import com.connection.cron.dto.supplierPart.SupplierPartRequestDto;
import com.connection.cron.po.supplierPart.SupplierPartPo;
import com.connection.cron.utils.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
@Slf4j
public class SupplierPartService {

  @Autowired
  private SupplierPartsDao supplierPartsDao;

  public List<Integer> getSupplierProductIdsForActiveSupplierParts(SupplierPartRequestDto supplierPartRequest) {
    return supplierPartsDao.getSupplierProductIdsForActiveSupplierParts(supplierPartRequest);
  }

  public List<SupplierPartDto> getActiveSupplierPartsBySupplierProductIds(List<Integer> supplierProductIds) {
    return supplierPartsDao.getActiveSupplierPartsBySupplierProductIds(supplierProductIds)
        .stream()
        .map(SupplierPartService::supplierPartPoToDto)
        .toList();
  }

  private static SupplierPartDto supplierPartPoToDto(SupplierPartPo source) {
    SupplierPartDto target = new SupplierPartDto();
    BeanUtils.copyPropertiesIgnoreErrors(target, source);
    return target;
  }
}
