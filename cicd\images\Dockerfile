# Stage 1: Download the Datadog Java Agent
FROM docker.io/curlimages/curl:latest AS downloader
WORKDIR /tmp
RUN yum update -y && yum install -y ca-certificates curl
RUN curl -fSL -o dd-java-agent.jar https://github.com/DataDog/dd-trace-java/releases/download/download-latest/dd-java-agent.jar

# Stage 2: Final runtime image
FROM eclipse-temurin:21-jdk-alpine

# Set working directory
WORKDIR /app

# Copy application files
COPY app.jar /app/app.jar
COPY cronrunner.conf /app/
COPY logback.xml /app/
COPY entrypoint.sh /app/

# Ensure the entrypoint script is executable
RUN chmod +x /app/entrypoint.sh

# Copy the downloaded Datadog Java Agent from the previous stage
COPY --from=downloader /tmp/dd-java-agent.jar /app/dd-java-agent.jar

ENTRYPOINT ["/app/entrypoint.sh"]