package com.connection.cron.job.dell.discontinue;

import com.connection.cron.dto.supplierPart.SupplierPartDto;
import com.connection.cron.job.CronJob;
import com.connection.cron.po.log.LogPo;
import com.connection.cron.po.mditem.MdItemPo;
import com.connection.cron.service.external.dell.discontinue.DellDiscontinueExternalApiService;
import com.connection.cron.service.log.LogService;
import com.connection.cron.service.mditem.MdItemService;
import com.connection.cron.service.supplierPart.dell.SupplierPartsDellService;
import com.connection.cron.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.sql.Date;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
@Qualifier("DiscontinueExpiredDellQuotePartsCronJob")
public class DiscontinueExpiredDellQuotePartsCronJob implements CronJob {

  public static final String JOB_NAME = DiscontinueExpiredDellQuotePartsCronJob.class.getSimpleName();

  @Autowired
  private MdItemService mdItemService;

  @Autowired
  private LogService logService;

  @Autowired
  private DellDiscontinueExternalApiService dellDiscontinueExternalApiService;

  @Autowired
  private SupplierPartsDellService supplierPartsDellService;

  @Value("${page-size.cron-job.DiscontinueExpiredDellQuotePartsCronJob:100}")
  private Integer pageSize;

  @Override
  public void execute() {
    log.info("Executing cron job [{}] started at {}", JOB_NAME, LocalDateTime.now());
    List<Integer> supplierProductIds
        = supplierPartsDellService.getSupplierProductIdsForActiveDellSupplierParts();
    int totalSize = supplierProductIds.size();
    for (int fromIndex = 0; fromIndex < totalSize; fromIndex += pageSize) {
      int toIndex = Math.min(fromIndex + pageSize, totalSize);
      List<Integer> batch = supplierProductIds.subList(fromIndex, toIndex);
      try {
        List<SupplierPartDto> activeDellSupplierParts = supplierPartsDellService.getActiveDellSupplierPartsBySupplierProductIds(batch);
        discontinueExpiredDellQuoteParts(activeDellSupplierParts);
      } catch (Exception e) {
        log.error("Failed to discontinue supplier product ids {} due to error {}", batch, e.getMessage());
      }
    }
  }

  private void discontinueExpiredDellQuoteParts(List<SupplierPartDto> batch) {
    populateDiscontinuedDateFromDellApi(batch);
    List<SupplierPartDto> partsToDiscontinue = getDiscontinuedSupplierParts(batch);
    discontinueMdItems(partsToDiscontinue);
    createDiscontinuedSupplierPartsLogs(partsToDiscontinue);
  }

  private static List<SupplierPartDto> getDiscontinuedSupplierParts(List<SupplierPartDto> batch) {
    return batch.stream()
        .filter(supplierPartPo -> supplierPartPo.getDiscontinueDate() != null && supplierPartPo.getDiscontinueDate()
            .isBefore(LocalDateTime.now()))
        .toList();
  }

  private void createDiscontinuedSupplierPartsLogs(List<SupplierPartDto> partsToDiscontinue) {
    List<LogPo> logs = partsToDiscontinue.stream()
        .map(supplierPart -> LogPo.builder()
            .action("UPDATE")
            .invoiceNum(0)
            .logDate(new Date(System.currentTimeMillis()))
            .logTime(DateUtils.getSecondsAfterMidnight())
            .orderNum(0)
            .program(JOB_NAME)
            .tableName("mditem")
            .fieldNames1("supp-code")
            .fieldNames2("supp-part")
            .fieldNames3("discontinued")
            .fieldValuesNew1(supplierPart.getSupplierCode())
            .fieldValuesNew2(supplierPart.getSupplierPart())
            .fieldValuesNew3("1")
            .fieldValuesOld1(supplierPart.getSupplierCode())
            .fieldValuesOld2(supplierPart.getSupplierPart())
            .fieldValuesOld3("0")
            .build())
        .toList();
    logService.batchInsertLogs(logs);
  }

  private void populateDiscontinuedDateFromDellApi(List<SupplierPartDto> batch) {
    batch.forEach(supplierPart -> {
      LocalDateTime discontinuedDate = dellDiscontinueExternalApiService.getDiscontinuedDate(
          supplierPart.getSupplierPart());
      log.info("Supplier part {} has discontinue date of {}", supplierPart.getSupplierPart(), discontinuedDate);
      supplierPart.setDiscontinueDate(discontinuedDate);
    });
  }

  private void discontinueMdItems(List<SupplierPartDto> partsToDiscontinue) {
    log.info("Discontinue supplier parts {}", partsToDiscontinue.stream()
        .map(SupplierPartDto::getSupplierPart)
        .toList());
    mdItemService.batchDiscontinue(partsToDiscontinue.stream()
        .map(supplierPart -> new MdItemPo(supplierPart.getSupplierCode(), supplierPart.getSupplierPart()))
        .toList());
  }
}
