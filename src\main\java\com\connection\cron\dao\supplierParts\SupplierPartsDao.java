package com.connection.cron.dao.supplierParts;

import com.connection.cron.dao.base.AbstractDataAccessObject;
import com.connection.cron.dto.supplierPart.SupplierPartRequestDto;
import com.connection.cron.po.supplierPart.SupplierPartPo;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Map;

@Component
public class SupplierPartsDao extends AbstractDataAccessObject {

  private final String ACTIVE_QUOTE_PART_SUPPLIER_IDS_QUERY = """
      SELECT supplier_products.id
       FROM dbase.mditem
       JOIN crux.suppliers
        ON suppliers.code = mditem.supp_code
       JOIN crux.supplier_products
        ON supplier_products.supplier_id = suppliers.id
        AND supplier_products.supplier_part = mditem.supp_part
       WHERE mditem.discontinued = 0
        AND mditem.supp_code IN (:supplierCodes)
        AND LENGTH(mditem.supp_part) BETWEEN :partLengthStart AND :partLengthEnd
        AND mditem.supp_part REGEXP :supplierPartRegexp
        AND mditem.mfg_name = :manufacturerName
        AND mditem.account <> ''
       ORDER BY DATE(supplier_products.created_on)
      """;

  private final String ACTIVE_QUOTE_PARTS_BY_SUPPLIER_IDS = """
      SELECT supplier_products.id as supplierProductId,
       mditem.supp_code as supplierCode,
       mditem.supp_part as supplierPart,
       mditem.mfg_name as manufacturerName,
       mditem.mfg_part as manufacturerPart,
       UPPER(mditem.account) as account,
       DATE(supplier_products.created_on) as supplierProductCreatedOnDate
       FROM dbase.mditem
       JOIN crux.suppliers
        ON suppliers.code = mditem.supp_code
       JOIN crux.supplier_products
        ON supplier_products.supplier_id = suppliers.id
        AND supplier_products.supplier_part = mditem.supp_part
       WHERE supplier_products.id IN (:supplierProductIds)
       ORDER BY DATE(supplier_products.created_on)
      """;

  public List<Integer> getSupplierProductIdsForActiveSupplierParts(SupplierPartRequestDto params) {
    Map<String, Object> queryParams = Map.of("supplierCodes", params.getSupplierCodes(), "partLengthStart",
        params.getSupplierPartLengthRange()
            .start(), "partLengthEnd", params.getSupplierPartLengthRange()
            .end(), "supplierPartRegexp", params.getSupplierPartRegex(), "manufacturerName",
        params.getManufacturerName());
    return getNamedParameterJdbcTemplate().queryForList(ACTIVE_QUOTE_PART_SUPPLIER_IDS_QUERY, queryParams,
        Integer.class);
  }

  public List<SupplierPartPo> getActiveSupplierPartsBySupplierProductIds(List<Integer> supplierProductIds) {

    return getNamedParameterJdbcTemplate().query(ACTIVE_QUOTE_PARTS_BY_SUPPLIER_IDS,
        Map.of("supplierProductIds", supplierProductIds), new BeanPropertyRowMapper<>(SupplierPartPo.class));
  }
}
