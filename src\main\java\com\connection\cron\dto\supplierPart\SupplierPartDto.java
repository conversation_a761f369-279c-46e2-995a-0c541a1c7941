package com.connection.cron.dto.supplierPart;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SupplierPartDto {
  private Integer supplierProductId;
  private String supplierCode;
  private String supplierPart;
  private String manufacturerName;
  private String manufacturerPart;
  private String account;
  private LocalDate supplierProductCreatedOnDate;
  private LocalDateTime discontinueDate;
}
