apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Release.Name }}
  labels:
    app: {{ .Release.Name }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ .Release.Name }}
  template:
    metadata:
      # Needed to force k8s to restart the pods when only the configmap is changed
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
      labels:
        app: {{ .Release.Name }}
    spec:
      containers:
        - name: {{ .Release.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ if hasSuffix "latest" .Values.image.tag }}Always{{ else }}IfNotPresent{{ end }}
          ports:
            - containerPort: {{ .Values.service.targetPort }}
              protocol: TCP
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: config-volume
              mountPath: /app/application.yml
              subPath: application.yml
          env:
            - name: ENVIRONMENT
              value: {{ .Values.environment }}
            {{- range .Values.env }}
            - name: {{ .name }}
              value: {{ .value | quote }}
            {{- end }}

            - name: MYSQL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ .Release.Name }}-secrets
                  key: MYSQL_PASSWORD

          # readinessProbe controls when k8s thinks a pod is ready for use and starts tearing
          # down the old pod. This is only applicable on a deploy
          readinessProbe:
            httpGet:
              path: /actuator/health
              port: 8080
            initialDelaySeconds: 400
            periodSeconds: 30
            failureThreshold: 10
            successThreshold: 1
            timeoutSeconds: 2

          # livenessProbe controls how k8s determines if a pod is not working
          # think of it as the health checks for pods
          livenessProbe:
            httpGet:
              path: /actuator/health
              port: 8080
            initialDelaySeconds: 1000
            periodSeconds: 15
      volumes:
        - name: config-volume
          # this mounts a config map of the application.yml file created in templates/configmap.yaml
          # the name of the config map is based on the app named provided by helm.
          # take a look at templates/_helpers.tpl for how this works.
          configMap:
            name: {{ include "my-app.fullname" . }}-config
      imagePullSecrets:
        - name: cnxn-docker
