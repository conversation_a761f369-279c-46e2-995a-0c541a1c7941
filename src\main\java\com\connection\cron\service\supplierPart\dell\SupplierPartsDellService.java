package com.connection.cron.service.supplierPart.dell;

import com.connection.cron.dto.basics.Range;
import com.connection.cron.dto.supplierPart.SupplierPartDto;
import com.connection.cron.dto.supplierPart.SupplierPartRequestDto;
import com.connection.cron.service.supplierPart.SupplierPartService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class SupplierPartsDellService {

  @Autowired
  private SupplierPartService supplierPartService;

  public List<Integer> getSupplierProductIdsForActiveDellSupplierParts() {
    return supplierPartService.getSupplierProductIdsForActiveSupplierParts(SupplierPartRequestDto.builder()
        .manufacturerName("DELL")
        .supplierCodes(List.of("D1", "BEN"))
        .supplierPartRegex("^[0-9\\.]+$")
        .supplierPartLengthRange(new Range(15, 17))
        .build());
  }

  public List<SupplierPartDto> getActiveDellSupplierPartsBySupplierProductIds(List<Integer> supplierProductIds) {
    return supplierPartService.getActiveSupplierPartsBySupplierProductIds(supplierProductIds);
  }
}
