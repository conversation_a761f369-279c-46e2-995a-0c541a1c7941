package com.connection.cron.dao.base;

import static lombok.AccessLevel.PROTECTED;

import jakarta.annotation.PostConstruct;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

@Component
public abstract class AbstractDataAccessObject {

  @Getter(PROTECTED)
  @Autowired
  @Qualifier("mysqlJdbcTemplate")
  private JdbcTemplate mysqlJdbcTemplate;

  @Getter(PROTECTED)
  private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

  @PostConstruct
  public void init() {
    this.namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(mysqlJdbcTemplate);
  }
}
