spring:
  application:
    name: cron-runner
  datasource:
    mysql:
      driver-class-name: com.mysql.cj.jdbc.Driver
      username: svc_apiuser
      password: ${MYSQL_PASSWORD}
      jdbc-url: ***********************************************************************************************************************************************************************************************************************************************************************************************
      hikari:
        maximum-pool-size: 10
        minimum-idle: 5
        idle-timeout: 30000
        max-lifetime: 1800000
        connection-timeout: 20000

    progress:
      jdbc-url: *********************************************************************************
      username: root
      password:
      driver-class-name: com.ddtek.jdbc.openedge.OpenEdgeDriver
      hikari:
        maximum-pool-size: 10
        minimum-idle: 5
        idle-timeout: 30000
        max-lifetime: 1800000
        connection-timeout: 20000

# patch size for cron jobs getting data from a datasource
page-size:
  cron-job:
    DiscontinueExpiredDellQuotePartsCronJob: 100

# cron job schedules
scheduled:
  cron-job:
    DiscontinueExpiredDellQuotePartsCronJob: 0 0 2 * * *

#Dell Discontinue api configs
dell:
  api:
    auth:
      url: https://apigtwb2c.us.dell.com/auth/oauth/v2/token
      client: l7da53da82dcac443981264ebf655ca169
      secret: e82058235e6641379e529e63c4ef32b1
      grantType: client_credentials
      refresh: 3600000
    discontinue:
      url: https://apigtwb2c.us.dell.com/PROD/QuoteSearchApi/api/v1/quote/
      lang: en-us

logging:
  level:
    com:
      zaxxer:
        hikari: ERROR

springdoc:
  api-docs:
    path: /api-docs