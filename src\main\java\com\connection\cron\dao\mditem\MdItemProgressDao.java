package com.connection.cron.dao.mditem;

import com.connection.cron.dao.base.AbstractProgressDataAccessObject;
import com.connection.cron.po.mditem.MdItemPo;
import org.springframework.jdbc.core.namedparam.SqlParameterSourceUtils;
import org.springframework.stereotype.Component;
import java.util.List;

@Component
public class MdItemProgressDao extends AbstractProgressDataAccessObject {

  public void batchDiscontinue(List<MdItemPo> mdItems) {
    getNamedParameterJdbcTemplate().batchUpdate("""
        UPDATE  "pub"."mditem"
           SET "discontinued" = 1
        WHERE "supp-code" = :supplierCode
        AND  "supp-part" = :supplierPart
        """, SqlParameterSourceUtils.createBatch(mdItems));
  }
}