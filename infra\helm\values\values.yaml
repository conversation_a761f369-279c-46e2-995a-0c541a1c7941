# Default values for the application
replicaCount: 2
environment: dev

image:
  repository: connection-cnxn-docker-virtual.jfrog.io/cron-runner
  tag: latest

service:
  type: ClusterIP
  port: 8080
  targetPort: 8080

ingress:
  enabled: true
  host: cron.dev.az.pcc.int
  path: /service

resources:
  limits:
    cpu: 500m
    memory: 2000Mi
  requests:
    cpu: 20m
    memory: 1000Mi

env: []

# External secret configuration
secretName: "cron-secrets"
