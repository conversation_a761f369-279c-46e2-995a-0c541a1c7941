JAVA_HOME="/usr/lib/jvm/jre-1.8.0-oracle.x86_64/"
JAVA_OPTS="-Xms2355m -Xmx2355m -Dorg.apache.tomcat.util.buf.UDecoder.ALLOW_ENCODED_SLASH=true"
JAVA_OPTS="$JAVA_OPTS -XX:+UseParallelGC"
JAVA_OPTS="$JAVA_OPTS -Dlogging.config=/app/logback.xml"
JAVA_OPTS="$JAVA_OPTS -Dorg.apache.coyote.ajp.DEFAULT_CONNECTION_TIMEOUT=600000"
JAVA_OPTS="$JAVA_OPTS -Djava.net.preferIPv4Stack=true -Djava.awt.headless=true -Dorg.apache.coyote.http11.Http11Protocol.SERVER=mdapi"
# Uncomment next line to enable heapdumps
# JAVA_OPTS="$JAVA_OPTS -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/opt/ces/api/heapdumps/"
JAVA_OPTS="$JAVA_OPTS --add-opens java.base/java.math=ALL-UNNAMED --add-opens java.base/java.lang=ALL-UNNAMED"

JAVA_OPTS="$JAVA_OPTS -Ddd.service.name=api -Ddd.service.mapping=datadirect:progress -Ddd.logs.injection=true -Ddd.trace.analytics.enabled=true -Ddd.trace.span.tags=release_version:JAVA17-SNAPSHOT -javaagent:/app/dd-java-agent.jar"

JAVA_OPTS="$JAVA_OPTS -Djavax.net.ssl.trustStore=/etc/ces/java_stores/cacerts"
