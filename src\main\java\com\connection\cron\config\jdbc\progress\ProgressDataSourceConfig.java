package com.connection.cron.config.jdbc.progress;

import com.zaxxer.hikari.HikariDataSource;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import javax.sql.DataSource;

@Configuration
public class ProgressDataSourceConfig {

  @Bean(name = "progressDataSource")
  @ConfigurationProperties("spring.datasource.progress")
  public DataSource progressDataSource() {
    return new HikariDataSource();
  }
}