package com.connection.cron.utils;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;

public class DateUtils {
  public static int getSecondsAfterMidnight() {
    ZonedDateTime nowTime = ZonedDateTime.now();
    ZonedDateTime midnight = nowTime.truncatedTo(ChronoUnit.DAYS);
    return (int) Duration.between(midnight, nowTime)
        .getSeconds();
  }
}

