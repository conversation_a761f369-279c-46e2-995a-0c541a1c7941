package com.connection.cron.aspect.timing;

import static org.apache.commons.lang3.time.DurationFormatUtils.formatDurationHMS;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
public class MethodExecutionTimeAspect {

  @Around("@annotation(com.connection.cron.annotation.timing.LogMethodExecutionTime)")
  public Object logExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
    StopWatch stopWatch = new StopWatch();
    Object proceed;
    try {
      stopWatch.start();
      proceed = joinPoint.proceed();
      stopWatch.stop();
    } finally {
      log.info("{} executed in {} ms", joinPoint.getSignature(), formatDurationHMS(stopWatch.getTime()));
    }

    return proceed;
  }
}