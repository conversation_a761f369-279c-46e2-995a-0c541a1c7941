{{- if .Values.ingress.enabled -}}
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: {{ .Release.Name }}-virtualservice
spec:
  hosts:
    - {{ .Values.ingress.host | quote }}
  gateways:
    - {{ .Release.Name }}-gateway
  http:
    - match:
        - uri:
            prefix:  {{ .Values.ingress.path }}
      route:
        - destination:
            host: {{ .Release.Name }}
            port:
              number: {{ .Values.service.port }}
{{- end }}
