package com.connection.cron.rest;

import com.connection.cron.CronRunnerManager;
import com.connection.cron.annotation.timing.LogMethodExecutionTime;
import lombok.extern.slf4j.Slf4j;
import org.apache.coyote.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.lang.reflect.Method;

@RestController
@RequestMapping("/api/cron")
@Slf4j
public class ApiController {

  @Autowired
  private CronRunnerManager cronRunnerManager;

  @GetMapping("/execute")
  @LogMethodExecutionTime
  public String runJob(@RequestParam("jobName") String jobName) throws Exception {
    final String methodName = "run" + jobName;
    try {
      Method method = cronRunnerManager.getClass().getDeclaredMethod(methodName);
      method.invoke(cronRunnerManager);
    } catch (Exception e) {
      log.error("Could not find method: {}", CronRunnerManager.class.getName() + "." + methodName, e);
      throw new BadRequestException("Could not find method: " + CronRunnerManager.class.getName() + "." + methodName, e);
    }
    return "OK";
  }
}
