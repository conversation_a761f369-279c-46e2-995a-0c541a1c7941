plugins {
    id 'java'
    id 'org.springframework.boot' version '3.3.5'
    id 'io.spring.dependency-management' version '1.1.6'
    id 'org.springdoc.openapi-gradle-plugin' version '1.9.0'
    id 'maven-publish'
}

group = 'com.connection'
version = '0.0.1-SNAPSHOT'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

repositories {
    mavenCentral()
    maven { url "https://repo.maven.apache.org/maven2" }
    maven { url "https://plugins.gradle.org/m2/" }
    maven {
        credentials {
            username "$mavenUser"
            password "$mavenPassword"
        }
        url "https://connection.jfrog.io/connection/ces-mvn-remote"
    }
    maven {
        credentials {
            username "$artifactoryReadUser"
            password "$artifactoryReadPassword"
        }
        url "https://connection.jfrog.io/connection/ces-mvn-release-local"
    }
    maven {
        credentials {
            username "$artifactoryReadUser"
            password "$artifactoryReadPassword"
        }
        url "https://connection.jfrog.io/connection/ces-mvn-snapshot-local"
    }
}

configurations {
    testImplementation.exclude group: 'com.vaadin.external.google', module: 'android-json'
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-data-jdbc'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.6.0'
    implementation 'org.springdoc:springdoc-openapi-starter-webflux-ui:2.6.0'
    implementation 'org.apache.commons:commons-lang3:3.0'
    implementation 'commons-beanutils:commons-beanutils:1.9.4'
    implementation 'com.mysql:mysql-connector-j'
    implementation 'ces:progress-driver-all:11.5.1'
    implementation 'org.json:json:20240303'
    implementation 'io.swagger.core.v3:swagger-annotations:2.2.25'
    implementation 'io.swagger.core.v3:swagger-models:2.2.25'
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
    compileOnly 'org.projectlombok:lombok:latest.release'
    annotationProcessor 'org.projectlombok:lombok:latest.release'
}

tasks.named('test') {
    useJUnitPlatform()
}

publishing {
    publications {
        maven(MavenPublication) {
            from components.java

            groupId = 'com.connection'
            artifactId = 'cron-runner'
            version = project.version
        }
    }

    repositories {
        maven {
            name = 'artifactory'
            url = version.endsWith('SNAPSHOT') ?
                'https://connection.jfrog.io/connection/ces-mvn-snapshot-local' :
                'https://connection.jfrog.io/connection/ces-mvn-release-local'
            credentials {
                username = project.findProperty('mavenDeployUser') ?: System.getenv('MAVEN_DEPLOY_USER')
                password = project.findProperty('mavenDeployPassword') ?: System.getenv('MAVEN_DEPLOY_PASSWORD')
            }
        }
    }
}
