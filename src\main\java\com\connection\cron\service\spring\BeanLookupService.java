package com.connection.cron.service.spring;

import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

@Component
public class BeanLookupService {

  private final ApplicationContext applicationContext;

  public BeanLookupService(ApplicationContext applicationContext) {
    this.applicationContext = applicationContext;
  }

  public <T> T getBeanByQualifier(Class<T> beanClass, String qualifier) {
    return applicationContext.getBean(qualifier, beanClass);
  }
}