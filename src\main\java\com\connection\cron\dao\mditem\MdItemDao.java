package com.connection.cron.dao.mditem;

import com.connection.cron.dao.base.AbstractDataAccessObject;
import com.connection.cron.po.mditem.MdItemPo;
import org.springframework.jdbc.core.namedparam.SqlParameterSourceUtils;
import org.springframework.stereotype.Component;
import java.util.List;

@Component
public class MdItemDao extends AbstractDataAccessObject {

  public void batchDiscontinue(List<MdItemPo> mdItems) {
    getNamedParameterJdbcTemplate().batchUpdate("""
        UPDATE dbase.mditem
           SET discontinued = 1
        WHERE supp_code = :supplierCode
        AND   supp_part = :supplierPart
        """, SqlParameterSourceUtils.createBatch(mdItems));
  }
}
