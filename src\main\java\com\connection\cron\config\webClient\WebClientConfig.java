package com.connection.cron.config.webClient;

import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import javax.net.ssl.SSLException;

@Component
public class WebClientConfig {
  @Bean
  public WebClient.Builder webClientBuilder() throws SSLException {
    return WebClient.builder();
  }
}
