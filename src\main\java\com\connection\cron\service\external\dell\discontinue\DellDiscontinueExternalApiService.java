package com.connection.cron.service.external.dell.discontinue;

import com.connection.cron.config.external.dell.discontinue.DellApiDiscontinueConfig;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;

@Service
@Slf4j
public class DellDiscontinueExternalApiService {

  @Autowired
  private DellApiDiscontinueConfig config;

  @Autowired
  private DellExternalAuthApiService authApiService;

  @Autowired
  private WebClient.Builder webClientBuilder;

  private WebClient webClient;

  @PostConstruct
  public void init() {
    this.webClient = webClientBuilder.baseUrl(config.getDiscontinueUrl())
        .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(1024 * 1024)) // Set to 1 MB
        .build();
  }

  public LocalDateTime getDiscontinuedDate(String supplierPart) {
    String authToken = authApiService.getJwtToken();
    if (authToken == null) {
      log.warn("Dell QuoteSearchApi authentication token is null");
      return null;
    }

    String[] supplierPartSplit = supplierPart.split("\\.", 2);
    String url = config.getDiscontinueUrl()
        .concat(supplierPartSplit[0])
        .concat("/")
        .concat(supplierPartSplit.length > 1 ? supplierPartSplit[1] : supplierPartSplit[0])
        .concat("/")
        .concat(config.getDiscontinueLang());
    try {
      String expirationDate = webClient.get()
          .uri(url)
          .header(HttpHeaders.USER_AGENT, "Connection's API")
          .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
          .retrieve()
          .onStatus(status -> status.is4xxClientError() || status.is5xxServerError(),
              clientResponse -> clientResponse.bodyToMono(String.class)
                  .flatMap(errorBody -> {
                    log.warn("Request failed with status {} and message {}", clientResponse.statusCode(),
                        new JSONObject(errorBody).getString("Message"));
                    return Mono.empty();
                  }))
          .bodyToMono(String.class)
          .map(body -> new JSONObject(body).getString("expirationDate"))
          .block();
      return expirationDate != null ? ZonedDateTime.parse(expirationDate)
          .toLocalDateTime() : null;
    } catch (Exception e) {
      log.error("Error during discontinued date request", e);
    }
    return null;
  }
}
