package com.connection.cron.config.openapi;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class OpenApiConfig {
  @Bean
  public OpenAPI authOpenAPI() {
    return new OpenAPI().info(new Info().title("CNXN Cron-Runner Api")
        .description("CNXN Cron-Runner api application")
        .version("v1.0")
        .license(new License().name("Apache 2.0")
            .url("https://springdoc.org")));
  }
}