pipeline {
  agent {
    docker {
      label 'azure'
      image 'k8s-deploy-tools:0.0.6'
      args "--add-host cnxn-dev-8edqgybd.b4556244-503a-4383-8571-ed956bc10c64.privatelink.eastus.azmk8s.io:************ -v /home/<USER>/container_passwd:/etc/passwd:ro"
      registryUrl 'https://connection-pfe-docker-local.jfrog.io'
      registryCredentialsId 'artifactory-pfe-remote'
    }
  }
  parameters {
    choice(name: 'DEPLOY_ENVIRONMENT', choices: ['dev', 'qa', 'uat','prod'])
    string(name: 'BRANCH',  defaultValue: 'develop')
    string(name: 'VERSION', defaultValue: '', description: 'Optional image version override (dev only)')
  }
  stages {
    stage ('Set build name and env vars') {
      steps {
        script {
          currentBuild.displayName = "${params.DEPLOY_ENVIRONMENT} #${currentBuild.number}"

          // Dynamically resolve AZURE_SUBSCRIPTION_ID from global env
          def upper = params.DEPLOY_ENVIRONMENT.toUpperCase()
          def varName = "AZURE_SUBSCRIPTION_ID_${upper}"
          env.AZURE_SUBSCRIPTION_ID = env."${varName}"

          // Conditionally set HELM_SET_VERSION for 'dev'
          env.HELM_SET_VERSION = (params.DEPLOY_ENVIRONMENT == 'dev' && params.VERSION?.trim())
            ? "--set image.tag=${params.VERSION}"
            : ''
        }
      }
    }

    stage ('Checkout API infra code') {
      steps {
        checkout([$class: 'GitSCM', branches: [[name: "*/$BRANCH"]],
          doGenerateSubmoduleConfigurations: false, submoduleCfg: [],
          userRemoteConfigs: [[credentialsId: '8edb6623-47ff-4239-8ee3-8a8f3ceabb55',
          url: 'https://versioncontrol:8443/com/moredirect/service']]])
      }
    }

    stage ('Deploy using helm charts') {
      steps {
        script {
          def credId = "aks-deploy-${params.DEPLOY_ENVIRONMENT}"
          withCredentials([
            usernamePassword(credentialsId: credId, usernameVariable: 'AKS_DEPLOY_USER', passwordVariable: 'AKS_DEPLOY_PASSWORD')
          ]) {
            sh """#!/bin/bash
              set -e
              az login --service-principal -u "$AKS_DEPLOY_USER" -p "$AKS_DEPLOY_PASSWORD" --tenant "75bbd831-230c-4b8e-ba6a-c9517320c1b1"
              az account set --subscription "$AZURE_SUBSCRIPTION_ID"
              az aks get-credentials --resource-group cnxn-${DEPLOY_ENVIRONMENT}-aks --name aks-${DEPLOY_ENVIRONMENT}-cnxn-eastus-k8s
              kubelogin convert-kubeconfig -l spn --client-id "$AKS_DEPLOY_USER" --client-secret "$AKS_DEPLOY_PASSWORD"
              cd infra/helm
              helm secrets upgrade \\
                --install \\
                --create-namespace api-core \\
                -f values/values.yaml \\
                -f values/values.${DEPLOY_ENVIRONMENT}.yaml \\
                -f secrets/${DEPLOY_ENVIRONMENT}.yaml \\
                -n api-core \\
                $HELM_SET_VERSION \\
                .
            """
          }
        }
      }
    }
  }
}
