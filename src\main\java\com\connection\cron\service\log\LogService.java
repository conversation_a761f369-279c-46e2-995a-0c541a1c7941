package com.connection.cron.service.log;

import static org.apache.commons.collections.CollectionUtils.isNotEmpty;

import com.connection.cron.dao.log.LogDao;
import com.connection.cron.dao.log.LogProgressDao;
import com.connection.cron.po.log.LogPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Service
public class LogService {

  @Autowired
  private LogDao logDao;

  @Autowired
  private LogProgressDao logProgressDao;

  @Transactional
  public void batchInsertLogs(List<LogPo> logEntries) {
    if (isNotEmpty(logEntries)) {
      logProgressDao.batchInsertLogs(logEntries);
      logDao.batchInsertLogs(logEntries);
    }
  }
}
